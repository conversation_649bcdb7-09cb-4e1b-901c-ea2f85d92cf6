#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.App {
  width: 100%;
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.card {
  padding: 2em;
  margin: 2em 0;
  border: 1px solid #ccc;
  border-radius: 8px;
  background-color: #f9f9f9;
  color: #333;
}

.read-the-docs {
  color: #888;
  font-size: 0.9em;
  margin-top: 2rem;
}

button {
  background-color: #61dafb;
  color: #282c34;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s;
}

button:hover {
  background-color: #21a9c7;
}

code {
  background-color: #f1f1f1;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

@media (prefers-color-scheme: dark) {
  .card {
    background-color: #1a1a1a;
    color: white;
    border-color: #333;
  }
  
  code {
    background-color: #333;
    color: #f1f1f1;
  }
}
