{"name": "portfolio", "version": "1.0.0", "type": "module", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@radix-ui/react-slot": "^1.2.3", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "gsap": "^3.13.0", "lucide-react": "^0.540.0", "react": "^19.1.1", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.1", "react-router-dom": "^7.8.1", "tailwind-merge": "^3.3.1", "three": "^0.179.1"}, "devDependencies": {"@vitejs/plugin-react": "^5.0.1", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.12", "vite": "^7.1.3"}}